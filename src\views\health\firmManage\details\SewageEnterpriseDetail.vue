<template>
  <div class="detail-container">
    <div class="detail-header">
      <h3>排污单位基本情况</h3>
    </div>
    <div class="detail-content">
      <table class="info-table">
        <tbody>
          <tr>
            <td class="label">是否需改正：</td>
            <td class="value middle-value">
              {{ formatYesNo(data.isShortPermit) }}
            </td>
            <td class="value highlight">
              符合《关于固定污染源排污限期整改有关事项的通知》要求的"不能达标排放""手续不全""其他"情形的，应勾选"是";确实不存在三种整改情形的，应勾选"否"
            </td>
          </tr>

          <tr>
            <td class="label">排污许可证管理类别：</td>
            <td class="value middle-value">
              {{ data.permitManageType || "-" }}
            </td>
            <td class="value highlight">
              排污单位属于《固定污染源排污许可分类管理名录》中排污许可重点管理的，应选择"重点"简化管理的选择"简化"。
            </td>
          </tr>

          <tr>
            <td class="label">注册地址：</td>
            <td class="value middle-value" colspan="2">
              {{ data.regAddress || "-" }}
            </td>
          </tr>

          <tr>
            <td class="label">生产经营场所地址：</td>
            <td class="value middle-value" colspan="2">
              {{ data.opeAddress || "-" }}
            </td>
          </tr>

          <tr>
            <td class="label">邮政编码：</td>
            <td class="value middle-value">{{ data.postCode || "-" }}</td>
            <td class="value highlight">生产经营场所地址所在地邮政编码。</td>
          </tr>

          <tr>
            <td class="label">行业类别：</td>
            <td class="value middle-value" colspan="2">
              {{ data.industryName || "-" }}
            </td>
          </tr>

          <tr>
            <td class="label">其他行业类别：</td>
            <td class="value middle-value" colspan="2">
              {{ data.industryParentName || "-" }}
            </td>
          </tr>

          <tr>
            <td class="label">是否投产：</td>
            <td class="value middle-value">否</td>
            <td class="value highlight">
              2015年1月1日起，正在建设过程中，或已建成但尚未投产的，选"否"；已经建成投产并产生排污行为的，选"是"。
            </td>
          </tr>

          <tr>
            <td class="label">投产日期：</td>
            <td class="value middle-value">{{ data.operaTime || "-" }}</td>
            <td class="value highlight">
              指已投运的排污单位正式投产运行的时间，对于分投运的排污单位，以先期投运时间为准。
            </td>
          </tr>

          <tr>
            <td class="label">生产经营场所中心经度：</td>
            <td class="value middle-value">{{ data.lng || "-" }}</td>
            <td class="value highlight">
              生产经营场所中心经纬度坐标，请点击"选择"按钮，在地图界面拾取坐标。
            </td>
          </tr>

          <tr>
            <td class="label">生产经营场所中心纬度：</td>
            <td class="value middle-value">{{ data.lat || "-" }}</td>
          </tr>

          <tr>
            <td class="label">组织机构代码：</td>
            <td class="value middle-value" colspan="2">
              {{ data.organCode || "-" }}
            </td>
          </tr>

          <tr>
            <td class="label">统一社会信用代码：</td>
            <td class="value middle-value" colspan="2">
              {{ data.creditCode || "-" }}
            </td>
          </tr>

          <tr>
            <td class="label">法定代表人（主要负责人）：</td>
            <td class="value middle-value" colspan="2">
              {{ data.legalPerson || "-" }}
            </td>
          </tr>

          <tr>
            <td class="label">技术负责人：</td>
            <td class="value middle-value" colspan="2">
              {{ data.concatPerson || "-" }}
            </td>
          </tr>

          <tr>
            <td class="label">固定电话：</td>
            <td class="value middle-value" colspan="2">
              {{ data.officePhone || "-" }}
            </td>
          </tr>

          <tr>
            <td class="label">移动电话：</td>
            <td class="value middle-value" colspan="2">
              {{ data.mobilePhone || "-" }}
            </td>
          </tr>

          <tr>
            <td class="label">所在地是否属于大气重点控制区域：</td>
            <td class="value middle-value" colspan="2">
              {{ formatYesNo(data.isSpecial) }}
            </td>
          </tr>

          <tr>
            <td class="label">所在地是否属于总磷控制区域：</td>
            <td class="value middle-value">
              {{ formatYesNo(data.isPhosphorus) }}
            </td>
            <td class="value highlight">
              指《国务院关于印发"十三五"生态环境保护规划的通知》(国发〔2016〕65号)以及生态环境部相关文件中确定的需要对总磷进行总量控制的区域。
            </td>
          </tr>

          <tr>
            <td class="label">所在地是否属于总氮控制区域：</td>
            <td class="value middle-value">
              {{ formatYesNo(data.isTotalNitrogen) }}
            </td>
            <td class="value highlight">
              指《国务院关于印发"十三五"生态环境保护规划的通知》(国发〔2016〕65号)以及生态环境部相关文件中确定的需要对总氮进行总量控制的区域。
            </td>
          </tr>

          <tr>
            <td class="label">
              所在地是否属于重金属污染物特别排放限值实施区域：
            </td>
            <td class="value middle-value" colspan="2">
              {{ formatYesNo(data.isHeavyMetalArea) }}
            </td>
          </tr>

          <tr>
            <td class="label">是否位于工业园区：</td>
            <td class="value middle-value" colspan="2">
              {{ formatYesNo(data.isPark) }}
            </td>
          </tr>

          <tr>
            <td class="label">所属工业园区名称：</td>
            <td class="value middle-value" colspan="2">
              {{ data.industrialParkName || "-" }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
export default {
  name: "SewageEnterpriseDetail",
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 组件内部数据
    };
  },
  created() {
    console.log(this.data, "======================");
  },
  methods: {
    // 格式化是否字段 (0-否,1-是)
    formatYesNo(value) {
      if (value === "1" || value === 1) return "是";
      if (value === "0" || value === 0) return "否";
      return "-";
    }
  }
};
</script>

<style scoped lang="scss">
.detail-container {
  height: 100%;

  .detail-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
    }
  }

  .detail-content {
    height: calc(100% - 60px);
    padding: 16px;
    overflow-y: auto;

    .info-table {
      width: 100%;
      table-layout: fixed;
      border-collapse: collapse;
      border: 1px solid #dddddd;

      td {
        padding: 8px 12px;
        font-size: 12px;
        word-break: break-all;
        vertical-align: middle;
        border: 1px solid #dddddd;

        &.label {
          width: 25%; // 第一栏占25%
          color: #333333;
          text-align: right;
          background-color: #f5f5f5;
        }

        &.value {
          color: #333333;
          text-align: left;
          background-color: #ffffff;

          &.highlight {
            color: #1111ff;
            text-align: justify;
          }

          // 中间列占50%
          &.middle-value {
            width: 50%;
          }
        }

        // 第三栏：标签+值的组合列，占25%
        &.label:nth-child(3) {
          width: 25%;
          background-color: #f5f5f5;
        }
      }

      tr {
        min-height: 40px;

        &:hover {
          background-color: #fafafa;
        }
      }
    }
  }
}
</style>
