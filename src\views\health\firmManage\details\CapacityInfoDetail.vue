<template>
  <div class="detail-container">
    <div class="detail-header">
      <h3>主要产品及产能补充</h3>
    </div>
    <div class="detail-content">
      <div class="table-container">
        <table class="capacity-table">
          <thead>
            <tr>
              <th>行业类别</th>
              <th>主要产品名称</th>
              <th>主要生产单元名称</th>
              <th>主要工艺名称</th>
              <th>生产设施名称</th>
              <th>设施参数名称</th>
              <th>设施参数设计值</th>
              <th>生产能力</th>
              <th>其他产品信息</th>
              <th>其他设施信息</th>
              <th>其他工艺信息</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="!capacityData || capacityData.length === 0">
              <td colspan="11" class="no-data">暂无数据</td>
            </tr>
            <tr v-for="(item, index) in capacityData" :key="index">
              <td>{{ item.industryName || "-" }}</td>
              <td>{{ item.productName || "-" }}</td>
              <td>{{ item.scdyName || "-" }}</td>
              <td>{{ item.technicsName || "-" }}</td>
              <td>{{ item.deviceName || "-" }}</td>
              <td>{{ item.paramName || "-" }}</td>
              <td>{{ formatParamDesign(item) }}</td>
              <td>{{ formatProdCapacity(item) }}</td>
              <td>{{ item.capaContent || "-" }}</td>
              <td>{{ item.facContent || "-" }}</td>
              <td>{{ item.technicsContent || "-" }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "CapacityInfoDetail",
  props: {
    data: {
      type: [Object, Array],
      default: () => []
    }
  },
  computed: {
    capacityData() {
      // 如果传入的是数组，直接返回
      if (Array.isArray(this.data)) {
        return this.data;
      }
      // 如果传入的是对象，尝试从对象中提取数组
      if (this.data && typeof this.data === "object") {
        // 可能的数组字段名
        const possibleArrayFields = ["list", "data", "items", "records"];
        for (const field of possibleArrayFields) {
          if (Array.isArray(this.data[field])) {
            return this.data[field];
          }
        }
        // 如果没有找到数组字段，将对象包装成数组
        return [this.data];
      }
      return [];
    }
  },
  methods: {
    // 格式化设施参数设计值（包含单位）
    formatParamDesign(item) {
      if (!item.paramDesign) return "-";
      const unit = item.paramUnitsName ? ` ${item.paramUnitsName}` : "";
      return `${item.paramDesign}${unit}`;
    },

    // 格式化生产能力（包含单位）
    formatProdCapacity(item) {
      if (!item.prodCapacity) return "-";
      const unit = item.prodUnitsName ? ` ${item.prodUnitsName}` : "";
      return `${item.prodCapacity}${unit}`;
    }
  }
};
</script>

<style scoped lang="scss">
.detail-container {
  height: 100%;

  .detail-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
    }
  }

  .detail-content {
    height: calc(100% - 60px);
    padding: 16px;
    overflow: auto;

    .table-container {
      width: 100%;
      overflow-x: auto;

      .capacity-table {
        width: 100%;
        min-width: 1200px; // 确保表格有足够宽度显示所有列
        font-size: 12px;
        table-layout: fixed;
        border-collapse: collapse;
        border: 1px solid #dddddd;

        th {
          padding: 8px 12px;
          font-weight: 500;
          color: #333333;
          text-align: center;
          white-space: nowrap;
          background-color: #f5f5f5;
          border: 1px solid #dddddd;

          // 设置各列宽度
          &:nth-child(1) {
            width: 8%;
          } // 行业类别
          &:nth-child(2) {
            width: 10%;
          } // 主要产品名称
          &:nth-child(3) {
            width: 12%;
          } // 主要生产单元名称
          &:nth-child(4) {
            width: 10%;
          } // 主要工艺名称
          &:nth-child(5) {
            width: 10%;
          } // 生产设施名称
          &:nth-child(6) {
            width: 10%;
          } // 设施参数名称
          &:nth-child(7) {
            width: 10%;
          } // 设施参数设计值
          &:nth-child(8) {
            width: 8%;
          } // 生产能力
          &:nth-child(9) {
            width: 10%;
          } // 其他产品信息
          &:nth-child(10) {
            width: 10%;
          } // 其他设施信息
          &:nth-child(11) {
            width: 12%;
          } // 其他工艺信息
        }

        td {
          padding: 8px 12px;
          color: #333333;
          text-align: center;
          word-break: break-all;
          vertical-align: middle;
          background-color: #ffffff;
          border: 1px solid #dddddd;

          &.no-data {
            font-style: italic;
            color: #999999;
            text-align: center;
          }
        }

        tbody tr {
          min-height: 40px;

          &:hover {
            background-color: #fafafa;

            td {
              background-color: #fafafa;
            }
          }
        }
      }
    }
  }
}
</style>
