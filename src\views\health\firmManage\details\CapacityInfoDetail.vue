<template>
  <div class="detail-container">
    <div class="detail-header">
      <h3>主要产品及产能补充</h3>
    </div>
    <div class="detail-content">
      <div class="table-container">
        <table class="capacity-table">
          <thead>
            <!-- 第一行表头 -->
            <tr>
              <th rowspan="2">生产编号和名称</th>
              <th rowspan="2">主要生产单元名称</th>
              <th rowspan="2">主要工艺名称</th>
              <th rowspan="2">生产设施名称</th>
              <th rowspan="2">生产设施编号</th>
              <th colspan="4">设施参数</th>
              <th rowspan="2">生产能力</th>
              <th rowspan="2">其他设施信息</th>
              <th rowspan="2">其他工艺信息</th>
            </tr>
            <!-- 第二行表头 -->
            <tr>
              <th>参数名称</th>
              <th>计量单位</th>
              <th>设计值</th>
              <th>其他设施参数信息</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="!data || data.length === 0">
              <td colspan="10" class="no-data">暂无数据</td>
            </tr>
            <tr v-for="(item, index) in data" :key="index">
              <td>{{ item.productName || "-" }}</td>
              <td>{{ item.scdyName || "-" }}</td>
              <td>{{ item.technicsName || "-" }}</td>
              <td>{{ item.deviceName || "-" }}</td>
              <td>{{ item.deviceCode || "-" }}</td>
              <td>{{ item.paramName || "-" }}</td>
              <td>{{ item.paramUnitsName || "-" }}</td>
              <td>{{ item.paramDesign || "-" }}</td>
              <td>{{ item.capaContent || "-" }}</td>
              <td>{{ item.prodCapacity || "-" }}</td>
              <td>{{ item.facContent || "-" }}</td>
              <td>{{ item.technicsContent || "-" }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "CapacityInfoDetail",
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  computed: {},
  methods: {}
};
</script>

<style scoped lang="scss">
.detail-container {
  height: 100%;

  .detail-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
    }
  }

  .detail-content {
    height: calc(100% - 60px);
    padding: 16px;
    overflow: auto;

    .table-container {
      width: 100%;
      overflow-x: auto;

      .capacity-table {
        width: 100%;
        min-width: 1200px; // 确保表格有足够宽度显示所有列
        font-size: 12px;
        table-layout: fixed;
        border-collapse: collapse;
        border: 1px solid #dddddd;

        th {
          padding: 8px 12px;
          font-weight: 500;
          color: #333333;
          text-align: center;
          background-color: #f5f5f5;
          border: 1px solid #dddddd;
        }

        td {
          padding: 8px 12px;
          color: #333333;
          text-align: center;
          word-break: break-all;
          vertical-align: middle;
          background-color: #ffffff;
          border: 1px solid #dddddd;

          &.no-data {
            font-style: italic;
            color: #999999;
            text-align: center;
          }
        }

        tbody tr {
          min-height: 40px;

          &:hover {
            background-color: #fafafa;

            td {
              background-color: #fafafa;
            }
          }
        }
      }
    }
  }
}
</style>
